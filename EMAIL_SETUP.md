# Email Setup Guide for CodeSafir Contact Form

The contact form is currently configured to send emails to `<EMAIL>` using a fallback mailto approach. For production use, you should integrate with a proper email service.

## Current Setup ✅ CONFIGURED

- **Recipient**: <EMAIL>
- **Primary Method**: Formspree (https://formspree.io/f/mjkobqga)
- **Fallback Method**: mailto (opens user's email client)
- **Form Fields**: Name, Email, Phone, Company (optional), Message
- **Status**: ✅ Ready for production use

## Recommended Email Services

### Option 1: EmailJS (Recommended)

EmailJS allows sending emails directly from the frontend without a backend server.

1. **Install EmailJS**:
   ```bash
   npm install emailjs-com
   ```

2. **Setup EmailJS Account**:
   - Go to [emailjs.com](https://www.emailjs.com/)
   - Create an account and get your Service ID, Template ID, and User ID
   - Create an email template with these variables:
     - `{{to_email}}` - <EMAIL>
     - `{{from_name}}` - sender's name
     - `{{from_email}}` - sender's email
     - `{{company}}` - sender's company
     - `{{message}}` - message content

3. **Update the code**:
   - Edit `src/lib/email.ts`
   - Uncomment the EmailJS section in `sendEmailViaEmailJS`
   - Replace placeholders with your actual IDs
   - Uncomment the EmailJS call in `sendContactEmail`

### Option 2: Formspree (CONFIGURED)

Formspree is a simple form backend service that is already configured for this project.

**Current Configuration**:
- **Form ID**: `mjkobqga`
- **Endpoint**: `https://formspree.io/f/mjkobqga`
- **Recipient**: <EMAIL>
- **Status**: ✅ Ready to use

The Formspree integration is already active and will be used automatically when the contact form is submitted.

### Option 3: Custom Backend

For more control, create your own backend API:

1. **Create API endpoint** (e.g., `/api/contact`)
2. **Use a service like**:
   - SendGrid
   - Mailgun
   - AWS SES
   - Nodemailer with SMTP

3. **Update the contact form** to call your API

## Environment Variables

The environment variables are already configured in the `.env` file:

```env
# Formspree (CONFIGURED) ✅
VITE_FORMSPREE_FORM_ID=mjkobqga

# EmailJS (Optional - for future use)
VITE_EMAILJS_SERVICE_ID=your_service_id
VITE_EMAILJS_TEMPLATE_ID=your_template_id
VITE_EMAILJS_USER_ID=your_user_id

# Custom API (Optional - for future use)
VITE_CONTACT_API_URL=your_api_endpoint
```

## Testing ✅ READY

The contact form is now configured with Formspree and ready for testing:

1. **Navigate to**: http://localhost:8080/contact (development) or your production URL
2. **Fill out the form** with all required fields:
   - Name (required)
   - Email (required)
   - Phone (required)
   - Company (optional)
   - Message (required)
3. **Submit the form** - it will send via <NAME_EMAIL>
4. **Check email** at <EMAIL> for the submission
5. **Verify** all form fields are included in the received email

**Expected behavior**:
- ✅ Form submits successfully via Formspree
- ✅ Email <NAME_EMAIL>
- ✅ All form data included in email
- ✅ Success message shown to user
- ✅ Form resets after successful submission

## Email Service Priority

The contact form uses the following priority order:

1. **Formspree** (Primary) ✅ - Configured and active
   - Sends <NAME_EMAIL>
   - Professional email delivery
   - No user interaction required

2. **Mailto Fallback** (Secondary) - Only if Formspree fails
   - Opens user's default email client
   - Pre-fills recipient, subject, and message
   - Requires user to manually send

This ensures reliable email delivery with a backup method if needed.
