import { cn } from "@/lib/utils";
import { LogoSpinner } from "./logo-spinner";

interface LoadingStateProps {
    message?: string;
    className?: string;
    size?: "sm" | "md" | "lg";
    variant?: "default" | "logo" | "simple";
    spinnerVariant?: "default" | "pulse" | "orbit" | "glow" | "bounce";
    fullScreen?: boolean;
}

export function LoadingState({
    message = "Loading...",
    className,
    size = "md",
    variant = "logo",
    spinnerVariant = "glow",
    fullScreen = false
}: LoadingStateProps) {
    const containerClasses = fullScreen
        ? "fixed inset-0 z-50 flex flex-col items-center justify-center bg-background/80 backdrop-blur-sm"
        : "flex flex-col items-center justify-center p-8 min-h-[300px] bg-background/50 backdrop-blur-sm rounded-lg border border-border/50";

    const renderSpinner = () => {
        switch (variant) {
            case "logo":
                return (
                    <LogoSpinner
                        size={size === "sm" ? "sm" : size === "lg" ? "lg" : "md"}
                        variant={spinnerVariant}
                        text={message}
                        showText={true}
                        speed="normal"
                    />
                );
            case "simple":
                return (
                    <LogoSpinner
                        size={size === "sm" ? "sm" : size === "lg" ? "lg" : "md"}
                        variant="default"
                        text={message}
                        showText={true}
                        speed="normal"
                    />
                );
            default:
                return (
                    <div className="flex flex-col items-center space-y-4">
                        <div className="relative">
                            <div className="w-16 h-16 border-4 border-primary/20 border-t-primary rounded-full animate-spin" />
                            <div className="absolute inset-2 border-2 border-secondary/20 border-b-secondary rounded-full animate-spin" style={{ animationDirection: 'reverse' }} />
                        </div>
                        <p className="text-sm text-muted-foreground font-medium">{message}</p>
                    </div>
                );
        }
    };

    return (
        <div className={cn(containerClasses, className)}>
            {renderSpinner()}
        </div>
    );
}