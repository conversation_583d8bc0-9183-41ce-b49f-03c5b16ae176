export interface Service {
  id: string;
  titleKey: string;
  descriptionKey: string;
  icon: string;
  features: string[];
}

export const services: Service[] = [
  {
    id: 'custom-web-apps',
    titleKey: 'home.services.customApps.title',
    descriptionKey: 'home.services.customApps.description',
    icon: 'Code',
    features: [
      'Modern React, Next.js & Vue.js frontends',
      'Node.js, Python & TypeScript backends',
      'REST, GraphQL & tRPC APIs',
      'Real-time applications with WebSockets',
      'Progressive Web Apps (PWAs)',
      'Responsive & mobile-first design',
      'Performance optimization & SEO',
      'Accessibility compliance (WCAG)'
    ]
  },
  {
    id: 'ecommerce',
    titleKey: 'home.services.ecommerce.title',
    descriptionKey: 'home.services.ecommerce.description',
    icon: 'ShoppingCart',
    features: [
      'Custom e-commerce platforms',
      'Shopify theme development & app integration',
      'WooCommerce & Magento solutions',
      'Multi-currency & payment gateway integration',
      'Inventory & order management systems',
      'Product recommendation engines',
      'Customer analytics & reporting dashboards',
      'Mobile commerce optimization'
    ]
  },
  {
    id: 'headless-cms',
    titleKey: 'home.services.headlessCms.title',
    descriptionKey: 'home.services.headlessCms.description',
    icon: 'Database',
    features: [
      'Contentful & Sanity.io implementation',
      'Strapi custom development',
      'Headless WordPress with Next.js',
      'Content modeling & information architecture',
      'API-first content delivery',
      'Multi-channel publishing workflows',
      'Content localization & internationalization',
      'Custom editor interfaces & extensions'
    ]
  },
  {
    id: 'cloud-migration',
    titleKey: 'home.services.cloudMigration.title',
    descriptionKey: 'home.services.cloudMigration.description',
    icon: 'Cloud',
    features: [
      'AWS, Azure & Google Cloud development',
      'Serverless application development',
      'Cloud-native architecture design',
      'Microservices development',
      'API Gateway & Lambda functions',
      'Cloud database integration',
      'Scalable backend solutions',
      'Performance optimization & monitoring'
    ]
  }
];
