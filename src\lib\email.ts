// Email service utility for sending contact form submissions
// This can be integrated with various email services

export interface EmailData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  message: string;
}

export interface EmailServiceResponse {
  success: boolean;
  message: string;
}

// EmailJS integration (recommended for client-side email sending)
export async function sendEmailViaEmailJS(data: EmailData): Promise<EmailServiceResponse> {
  try {
    // EmailJS configuration would go here
    // You need to:
    // 1. Install emailjs-com: npm install emailjs-com
    // 2. Set up EmailJS account and get service ID, template ID, and user ID
    // 3. Create email template in EmailJS dashboard

    /*
    const emailjs = await import('emailjs-com');
    
    const templateParams = {
      to_email: '<EMAIL>',
      from_name: data.name,
      from_email: data.email,
      phone: data.phone || 'Not provided',
      company: data.company || 'Not specified',
      message: data.message,
      subject: `New Contact Form Submission from ${data.name}`,
    };

    const result = await emailjs.send(
      'YOUR_SERVICE_ID',
      'YOUR_TEMPLATE_ID', 
      templateParams,
      'YOUR_USER_ID'
    );

    return {
      success: true,
      message: 'Email sent successfully!'
    };
    */

    // For now, return a simulated success
    console.log('Email data prepared for sending:', data);
    return {
      success: true,
      message: 'Email prepared for sending'
    };
  } catch (error) {
    console.error('EmailJS error:', error);
    return {
      success: false,
      message: 'Failed to send email via EmailJS'
    };
  }
}

// Formspree integration (alternative service)
export async function sendEmailViaFormspree(data: EmailData): Promise<EmailServiceResponse> {
  try {
    // You need to:
    // 1. Sign up at formspree.io
    // 2. Get your form endpoint
    // 3. Replace 'YOUR_FORM_ID' with your actual form ID

    const formId = import.meta.env.VITE_FORMSPREE_FORM_ID || 'mjkobqga';
    const response = await fetch(`https://formspree.io/f/${formId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: data.name,
        email: data.email,
        phone: data.phone,
        company: data.company,
        message: data.message,
        _replyto: data.email,
        _subject: `New Contact Form Submission from ${data.name}`,
      }),
    });

    if (response.ok) {
      return {
        success: true,
        message: 'Email sent successfully!'
      };
    } else {
      throw new Error('Formspree request failed');
    }
  } catch (error) {
    console.error('Formspree error:', error);
    return {
      success: false,
      message: 'Failed to send email via Formspree'
    };
  }
}

// Mailto fallback (opens user's email client)
export function sendEmailViaMailto(data: EmailData): EmailServiceResponse {
  const subject = `New Contact Form Submission from ${data.name}`;
  const body = `
Name: ${data.name}
Email: ${data.email}
${data.phone ? `Phone: ${data.phone}` : ''}
${data.company ? `Company: ${data.company}` : ''}

Message:
${data.message}

---
This message was sent from the CodeSafir contact form.
  `.trim();

  const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

  try {
    window.open(mailtoLink, '_blank');
    return {
      success: true,
      message: 'Email client opened. Please send the message.'
    };
  } catch (error) {
    console.error('Mailto error:', error);
    return {
      success: false,
      message: 'Failed to open email client'
    };
  }
}

// Main email sending function with fallback strategy
export async function sendContactEmail(data: EmailData): Promise<EmailServiceResponse> {
  // Check if EmailJS is configured
  if (import.meta.env.VITE_EMAILJS_SERVICE_ID &&
    import.meta.env.VITE_EMAILJS_TEMPLATE_ID &&
    import.meta.env.VITE_EMAILJS_USER_ID) {
    const emailJSResult = await sendEmailViaEmailJS(data);
    if (emailJSResult.success) {
      return emailJSResult;
    }
  }

  // Check if Formspree is configured (always try Formspree first since it's configured)
  const formspreeResult = await sendEmailViaFormspree(data);
  if (formspreeResult.success) {
    return formspreeResult;
  }

  // Fallback to mailto
  return sendEmailViaMailto(data);
}
