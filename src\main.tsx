import { createRoot } from 'react-dom/client'
import { Suspense } from 'react'
import App from './App.tsx'
import './index.css'
import './i18n/i18n'
import { LoadingState } from './components/ui/loading-state'

// Add error handling for initial render
const root = document.getElementById("root");
if (!root) {
    throw new Error("Root element not found");
}

createRoot(root).render(
    <Suspense fallback={
        <LoadingState
            message="Loading application..."
            size="lg"
            variant="logo"
            spinnerVariant="glow"
            fullScreen={true}
        />
    }>
        <App />
    </Suspense>
);
